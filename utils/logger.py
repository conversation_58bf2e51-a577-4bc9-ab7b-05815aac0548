import logging
import sys
import shutup
import warnings
import os
import multiprocessing
import subprocess
import functools
import time
import traceback
import inspect
import asyncio
from typing import Callable, Dict, Any, List
from tabulate import tabulate

class ProcessInfoMixin:
    @staticmethod
    def get_supervisor_info():
        try:
            proc_name = os.environ.get('SUPERVISOR_PROCESS_NAME', 'Unknown')
            parent_pid = os.getppid()
            ps_output = subprocess.check_output(['ps', '-p', str(parent_pid), '-o', 'comm=']).decode().strip()
            return f"{proc_name}|{ps_output}"
        except Exception:
            return "Unknown"

class SubtleGradientColoredFormatter(logging.Formatter):
    COLORS = {
        "DEBUG": ["\033[94;1m"],
        "INFO": ["\033[92m"],
        "WARNING": ["\033[93m"],
        "ERROR": ["\033[91m"],
        "CRITICAL": ["\033[95m"],
    }
    RESET = "\033[0m"

    def format(self, record):
        level_colors = self.COLORS.get(record.levelname, [self.RESET])
        color_index = min(record.levelno // 10, len(level_colors) - 1)
        gradient_color = level_colors[color_index]

        record.process_info = ProcessInfoMixin.get_supervisor_info()
        record.file_info = os.path.basename(record.pathname) if hasattr(record, 'pathname') else "unknown"

        message = super().format(record)
        return f"{gradient_color}{message}{self.RESET}"

class CustomLoggerAdapter(logging.LoggerAdapter, ProcessInfoMixin):
    def __init__(self, logger, logger_name):
        super().__init__(logger, {})
        self.logger_name = logger_name
        self.process_name = multiprocessing.current_process().name

    def process(self, msg, kwargs):
        extra = kwargs.pop('extra', {})
        msg = f"{self.logger_name} - {msg}"
        return msg, kwargs

    def log_dict_as_table(self, data: Dict[str, Any], title: str = None, headers=["Key", "Value"], tablefmt="rounded_grid"):
        """Log dictionary data as a formatted table.

        Args:
            data: Dictionary to be logged
            title: Optional title for the table
            headers: Table headers (default: ["Key", "Value"])
            tablefmt: Table format (default: grid)
        """
        def safe_str_convert(value):
            """Safely convert any value to string for tabulate"""
            try:
                if isinstance(value, bool):
                    return str(value)
                elif isinstance(value, (dict, list)):
                    return str(value)
                elif value is None:
                    return "None"
                else:
                    return str(value)
            except Exception as e:
                return f"<Error converting value: {type(value).__name__}>"

        # Convert all data to safe strings
        safe_table_data = []
        for k, v in data.items():
            try:
                key_str = str(k)
                value_str = safe_str_convert(v)
                safe_table_data.append([key_str, value_str])
            except Exception as e:
                safe_table_data.append([str(k), f"<Error: {e}>"])

        table = tabulate(safe_table_data, headers=headers, tablefmt=tablefmt, maxcolwidths=[None, 100])
        if title:
            self.info(f"\n{title}:\n{table}")
        else:
            self.info(f"\n{table}")

    def log_list_of_dicts(self, data: List[Dict[str, Any]], title: str = None, headers="keys", tablefmt="grid"):
        """Log list of dictionaries as a formatted table.
        
        Args:
            data: List of dictionaries to be logged
            title: Optional title for the table
            headers: Table headers (default: dictionary keys)
            tablefmt: Table format (default: grid)
        """
        if not data:
            self.warning("Empty data list provided for table logging")
            return
            
        table = tabulate(data, headers=headers, tablefmt=tablefmt)
        if title:
            self.info(f"\n{title}:\n{table}")
        else:
            self.info(f"\n{table}")

def setup_new_logging(logger_name):
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)

    if logger.hasHandlers():
        logger.handlers.clear()

    log_format = SubtleGradientColoredFormatter(
        '%(asctime)s - %(levelname)s - [%(funcName)s] - %(process_info)s - %(file_info)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    stdout_handler = logging.StreamHandler(sys.stdout)
    stdout_handler.setLevel(logging.DEBUG)
    stdout_handler.setFormatter(log_format)

    logger.addHandler(stdout_handler)
    logger.propagate = False

    return CustomLoggerAdapter(logger, logger_name)

class FunctionLoggingHandler:
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def __call__(self, func: Callable) -> Callable:
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    self.logger.info(f"Starting: {func.__name__}")
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    self.logger.error(f"Error in {func.__name__}: {e}")
                    raise
                finally:
                    execution_time = time.time() - start_time
                    time_msg = f"({execution_time:.2f}s)"
                    if execution_time > 1:
                        time_msg = f"\033[91m{time_msg}\033[0m"
                    self.logger.info(f"Completed: {func.__name__} {time_msg}")
            return async_wrapper
        else:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    self.logger.info(f"Starting: {func.__name__}")
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    self.logger.error(f"Error in {func.__name__}: {e}")
                    raise
                finally:
                    execution_time = time.time() - start_time
                    time_msg = f"({execution_time:.2f}s)"
                    if execution_time > 1:
                        time_msg = f"\033[91m{time_msg}\033[0m"
                    self.logger.info(f"Completed: {func.__name__} {time_msg}")
            return wrapper

def apply_logging_to_all_functions(module_globals: dict) -> None:
    logger = setup_new_logging(__name__)
    function_logger = FunctionLoggingHandler(logger)
    
    for name, obj in module_globals.items():
        if name.startswith('__') or not callable(obj) or inspect.isclass(obj):
            continue
        module_globals[name] = function_logger(obj)