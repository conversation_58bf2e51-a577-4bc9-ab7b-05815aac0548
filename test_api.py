"""
Test script for the multi-tenant FastAPI application
Tests login and chat endpoints
"""

import requests
import json

# API base URL
BASE_URL = "http://localhost:8000"

def test_login():
    """Test the login endpoint"""
    print("🔐 Testing login endpoint...")
    
    login_data = {
        "username": "admin",
        "password": "admin123",
        "client_id": "ambition-guru"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Login successful!")
            print(f"   Token: {result['access_token'][:50]}...")
            print(f"   User: {result['username']} ({result['role']})")
            print(f"   Tenant: {result['tenant_label']}")
            return result['access_token']
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None


def test_chat(token):
    """Test the chat endpoint"""
    print("\n💬 Testing chat endpoint...")
    
    if not token:
        print("❌ No token available for chat test")
        return
    
    chat_data = {
        "message": "Hello! What courses do you offer?"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json=chat_data,
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat successful!")
            print(f"   User message: {chat_data['message']}")
            print(f"   Bot response: {result['response'][:100]}...")
            print(f"   Thread ID: {result['thread_id']}")
        else:
            print(f"❌ Chat failed: {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Chat error: {e}")


def test_verify_token(token):
    """Test the verify token endpoint"""
    print("\n🔍 Testing verify token endpoint...")
    
    if not token:
        print("❌ No token available for verification test")
        return
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/v1/verify_token",
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Token verification successful!")
            print(f"   Valid: {result['valid']}")
            print(f"   User: {result['username']} ({result['role']})")
            print(f"   Tenant ID: {result['tenant_id']}")
        else:
            print(f"❌ Token verification failed: {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Token verification error: {e}")


def test_health_check():
    """Test the health check endpoint"""
    print("\n🏥 Testing health check endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/health")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Health check successful!")
            print(f"   Status: {result['status']}")
            print(f"   Service: {result['service']}")
            print(f"   Version: {result['version']}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Health check error: {e}")


if __name__ == "__main__":
    print("🚀 Starting API tests...")
    print("="*50)
    
    # Test health check first
    test_health_check()
    
    # Test login
    token = test_login()
    
    # Test token verification
    test_verify_token(token)
    
    # Test chat
    test_chat(token)
    
    print("\n" + "="*50)
    print("🎯 API tests completed!")
    
    if token:
        print("\n📝 You can now use the following for manual testing:")
        print(f"   Authorization Header: Bearer {token}")
        print(f"   API Documentation: {BASE_URL}/docs")
    else:
        print("\n❌ Login failed - check your setup and try again")
