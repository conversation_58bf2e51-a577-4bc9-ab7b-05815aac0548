from fastapi import APIRouter, HTTPException, Depends

import jwt
from src.core.config import SECRET_KEY, ALGORITHM
from src.helper import logger
from src.core.security import  get_tenant_info
from src.core.database import get_db_from_tenant_id, get_tenant_id_and_name_from_slug
from src.models.security import OAuth2PasswordRequestFormWithClientID, ChangePasswordRequest, ResetPasswordRequest, ExtendedTokenRequest, get_login_form_with_referrer_check
from src.core.security import (
    create_access_token,
    verify_password,
    create_invitation_token,
    verify_invitation_token,
     get_tenant_info,
   min_role,
    pwd_context
)
from src.models.user import AgentInvitation, AgentRegistration
from src.helper import convert_objectid_to_str
from datetime import timedelta
from src.models.user import UserTenantDB
from datetime import datetime, timedelta, timezone
loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["Users"])


@router.get("/get_tenant_id")
async def tenantid_from_slug(slug: str):
    try:
        result = get_tenant_id_and_name_from_slug(slug)
        tenant_id = str(result["_id"])
        tenant_name = result["name"]

        return{
            "tenant_id":tenant_id,
            "tenant_name":tenant_name
        }
    except Exception:
        raise Exception

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends(get_login_form_with_referrer_check)):
    # The client_id will be automatically set to "dramit" if the request comes from localhost:3000 or 127.0.0.1:3000
    # Log the client_id being used
    loggers.info(f"Processing login with client_id: {form_data.client_id}")
    print("clientid", form_data.client_id)

    # find the database name of that tenant
    result = get_tenant_id_and_name_from_slug(form_data.client_id)
    if not result:
        raise HTTPException(status_code=404, detail="Tenant not found")
    tenant_id = str(result["_id"])
    tenant_database = get_db_from_tenant_id(tenant_id)
    print(tenant_database)
    # connect to the user_collection of that database.
    user = tenant_database.users.find_one({"username": form_data.username})
    try:
        nav_permission=tenant_database.settings.find_one({"name":"nav_permission"}).get(user["role"])
    except Exception as e:
        nav_permission=None
    if not user :
        raise HTTPException(status_code=401, detail="User not found")

    if not verify_password(form_data.password, user["hashed_password"]):
        raise HTTPException(status_code=401, detail="Incorrect Credentials")
    days = 0
    hours = 0
    minutes = 0
    seconds = 0
    try:
        # Get token validity settings from database
        token_settings = tenant_database.settings.find_one({"name":"token_validity"})

        # Extract days, hours, minutes, seconds from settings
        days = token_settings.get("days", 0)
        hours = token_settings.get("hours", 0)
        minutes = token_settings.get("minutes", 0)
        seconds = token_settings.get("seconds", 0)

        # Create timedelta with all components
        expires_delta = timedelta(
            days=days,
            hours=hours,
            minutes=minutes,
            seconds=seconds
        )
        loggers.info(f"Token validity settings: {token_settings}")

        # If no time components were found, use default
        if days == 0 and hours == 0 and minutes == 0 and seconds == 0:
            loggers.warning("No token validity settings found. Using default (6 hours).")
            expires_delta = timedelta(hours=6)  # Default: 6 hours

    except Exception as e:
        # Fallback to 6 hours if any error occurs
        expires_delta = timedelta(hours=6)
        print(f"Error fetching token validity settings: {str(e)}. Using default (6 hours).")

    access_token = create_access_token(
        data={"sub": user["username"],"user_id": str(user["_id"]), "role": user["role"], "tenant_id": tenant_id},
        expires_delta=expires_delta
    )

    # Calculate expiration date for response
    expiration_date = datetime.now(timezone.utc) + expires_delta


    user = convert_objectid_to_str(user)

    print(result)

    return {
        "id": user["_id"],
        "access_token": access_token,
        "token_type": "bearer",
        "username": user['username'],
        "role": user['role'],
        "tenant_id": tenant_id,
        "tenant_label": result["label"],
        "tenant_slug": form_data.client_id,
        "nav_permission": nav_permission,
        "token_validity": {
            "days": days,
            "hours": hours,
            "minutes": minutes,
            "seconds": seconds,
            "total_seconds": expires_delta.total_seconds()
        },
        "expires_at": expiration_date.isoformat()
    }





@router.get("/verify_token")
async def verify_token(current_user: UserTenantDB = Depends(get_tenant_info)):
    """If the token is valid, return the user's details
       If the token is invalid, get_current_user will raise an exception
    """
    # Return basic user information to confirm token is valid
    return {
        "valid": True,
        "username": current_user.user.username,
        "role": current_user.user.role,
        "tenant_id": current_user.tenant_id
    }


@router.post("/extended_token")
async def get_extended_token(request: ExtendedTokenRequest):
    """Generate an access token with extended validity period.

    Args:
        request: The request containing username, password, client_id, and days for token validity

    Returns:
        A dictionary containing the access token and user information
    """
    # Find the tenant database
    result = get_tenant_id_and_name_from_slug(request.client_id)
    if not result:
        raise HTTPException(status_code=404, detail="Tenant not found")

    tenant_id = str(result["_id"])
    tenant_database = get_db_from_tenant_id(tenant_id)

    # Authenticate the user
    user = tenant_database.users.find_one({"username": request.username})
    if not user:
        raise HTTPException(status_code=401, detail="User not found")

    if not verify_password(request.password, user["hashed_password"]):
        raise HTTPException(status_code=401, detail="Incorrect credentials")

    # Generate token with extended validity
    access_token = create_access_token(
        data={"sub": user["username"], "user_id": str(user["_id"]), "role": user["role"], "tenant_id": tenant_id},
        expires_delta=timedelta(days=request.days)
    )

    # Get navigation permissions if available
    try:
        nav_permission = tenant_database.settings.find_one({"name": "nav_permission"}).get(user["role"])
    except Exception:
        nav_permission = None

    # Convert ObjectId to string for JSON serialization
    user = convert_objectid_to_str(user)

    # Log the extended token generation
    loggers.info(f"Extended token generated for user {request.username} with validity of {request.days} days")

    return {
        "id": user["_id"],
        "access_token": access_token,
        "token_type": "bearer",
        "username": user['username'],
        "role": user['role'],
        "tenant_id": tenant_id,
        "tenant_label": result["label"],
        "tenant_slug": request.client_id,
        "nav_permission": nav_permission,
        "token_validity": {
            "days": request.days,
            "hours": 0,
            "minutes": 0,
            "seconds": 0,
            "total_seconds": timedelta(days=request.days).total_seconds()
        },
        "expires_at": (datetime.now(timezone.utc) + timedelta(days=request.days)).isoformat()
    }


@router.post("/agents/invite")
async def invite_agent(
    invitation: AgentInvitation,
    current_user: UserTenantDB = Depends(get_tenant_info)
    ):
    """
    Invite a new agent by generating a registration link with a token.
    Only admins and supervisors can invite agents.
    """

    # invited_by = "god"
    print(current_user)
    tenant_id = current_user.tenant_id
    users_collection = current_user.db.users
    tenant_role = current_user.user.role
    slug = current_user.slug
    if tenant_role not in ["admin", "supervisor"]:
        raise HTTPException(status_code=403, detail="You are not authorized to invite agents")

    invited_by = str(current_user.user.id)
    print(invited_by)

    existing_user = users_collection.find_one({"username": invitation.username})
    if existing_user:
        return {"registration_token": None, "success": False, "msg": "Username already exists"}

    invitations_collection = current_user.db.invitations
    existing_invitation = invitations_collection.find_one({"username": invitation.username})
    if existing_invitation:
        try:
            result = invitations_collection.delete_many({"username": invitation.username})
            if result.deleted_count > 0:
                loggers.info(f"Record with username '{invitation.username}' existed, therefore deleted existing invitation to create new one.")
        except HTTPException as e:
            loggers.error(e)
            raise e

    # # Create invitation token
    token = create_invitation_token(username=invitation.username,
                                    role=invitation.role,
                                    invited_by=invited_by,
                                    tenant_id=tenant_id,
                                    slug=slug,
                                    expires_delta=timedelta(days=invitation.expires_at))

    invitation_record = {
        "username": invitation.username,
        "slug": slug,
        "token": token,
        "role": invitation.role,
        "invited_by": invited_by,
        "expires_at": datetime.now() + timedelta(days=invitation.expires_at),
        "used": False,
        "permissions":invitation.permissions
    }

    invitations_collection.insert_one(invitation_record)

    return {"registration_token": token, "success": True, "msg": "Token Generated!"}


@router.post("/agents/register")
async def register_agent(registration: AgentRegistration):
    """
    Register a new agent using the invitation token.
    """
    # Verify the token and extract the agent's name
    try:
        agent_username, tenant_id,invited_by, role_ = verify_invitation_token(registration.token)
    except HTTPException as e:
        loggers.error(e)
        raise e

    tenant_database =  get_db_from_tenant_id(tenant_id)
    invitations_collection = tenant_database.invitations
    invitation = invitations_collection.find_one({"token": registration.token})

    if not invitation:
        return {"msg": "Invalid invitation token!", "success": False}
    if invitation.get("used"):
        return {"msg": "Invitation token has already been used. Request the supervisor to generate a new one!", "success": False}

    users_collection = tenant_database.users
    # Check if the desired username already exists
    existing_user = users_collection.find_one({"username": agent_username})
    if existing_user:
        return {"msg": "Username already exists!", "success": False}

    # Hash the provided password
    hashed_password = pwd_context.hash(registration.password)

    # Create the new agent user
    new_agent = {
        "username": registration.username,
        "username": registration.username,
        "hashed_password": hashed_password,
        "role": role_,
        "created_by": invited_by,
        "created_at": datetime.now(),
        "permissions":invitation.get('permissions')
    }

    # Insert the new agent into the database
    result = users_collection.insert_one(new_agent)
    new_agent["_id"] = result.inserted_id

    invitations_collection.update_one(
        {"token": registration.token},
        {"$set": {"used": True}}
    )

    return {"msg": "Agent registered successfully", "success": True}

@router.get("/users/roles/count")
async def get_user_counts_by_role(current_user: UserTenantDB = Depends(get_tenant_info)):
    user_role = current_user.user.role
    try:
        if user_role != "admin":
            raise HTTPException(status_code=403, detail="You are not authorized to access this information")
    except Exception as e:
        loggers.error(f"Authorization error: {e}")
        raise e

    users_collection = current_user.db.users

    agents = users_collection.find({"role": "agent"}, {"_id": 0, "username": 1}).to_list(None)
    admins = users_collection.find({"role": "admin"}, {"_id": 0, "username": 1}).to_list(None)

    total_agents = len(agents)
    total_admins = len(admins)
    total_users = total_agents + total_admins

    return {
        "Total_Users": total_users,
        "Agents": {
            "names": [user["username"] for user in agents],
            "count": total_agents
        },
        "Admins": {
            "names": [user["username"] for user in admins],
            "count": total_admins
        }
    }