"""
Search Agent V2 - Modern LangChain implementation with proper memory management
Uses LangGraph's built-in memory patterns instead of hardcoded context handling
"""

import logging
from typing import Dict, Any
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import Cha<PERSON><PERSON><PERSON><PERSON><PERSON>emplate
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import START, MessagesState, StateGraph
import os
from dotenv import load_dotenv

from config import get_vector_store_manager

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)


class SearchAgentV2:
    """
    Search Agent V2 - Modern implementation using LangGraph memory patterns
    Replaces hardcoded context handling with proper LangChain memory management
    """
    
    def __init__(self):
        """Initialize with global vector store and LangGraph memory"""
        # Use the global vector store manager (initialized only once)
        self.vector_manager = get_vector_store_manager()
        
        # Initialize LLM for query translation
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        
        # Create memory for conversation context using LangGraph patterns
        self.memory = MemorySaver()
        
        # Create query translation workflow
        self._setup_query_translator()
        
        logger.info("✅ Search Agent V2 initialized with LangGraph memory patterns")
    
    def _setup_query_translator(self):
        """Setup LangGraph workflow for intelligent query translation"""
        
        # Define the state schema for query translation
        workflow = StateGraph(state_schema=MessagesState)
        
        def translate_query(state: MessagesState):
            """Translate user query based on conversation context"""
            
            # Get conversation history from state
            messages = state["messages"]
            current_message = messages[-1].content if messages else ""
            
            # Create context from recent messages
            context_messages = messages[-5:] if len(messages) > 1 else []
            context_text = " ".join([msg.content for msg in context_messages[:-1]])
            
            # System prompt for query translation
            system_prompt = """You are a query translator that converts user messages into effective search queries.

Based on the conversation context and current message, determine:
1. The best search query to use
2. Whether this is an "information" search (troubleshooting, apps, services) or "products" search (courses, programs)

Context analysis rules:
- If previous messages mention problems ("chalena", "not working", "issue", "error"), treat as troubleshooting
- If user mentions specific apps like "Ambition Guru" with problem context, focus on that app's troubleshooting
- If asking about courses without problem context, treat as product search
- If asking "what is" about an app mentioned before, provide app information

Current message: {current_message}
Recent conversation context: {context}

Respond with:
1. Translated query (optimized for search)
2. Search type: "information" or "products"
3. Brief reasoning

Format: 
Query: [your translated query]
Type: [information/products]
Reasoning: [brief explanation]"""

            prompt = ChatPromptTemplate.from_messages([
                ("system", system_prompt),
                ("human", "Translate this query: {current_message}\nContext: {context}")
            ])
            
            # Get translation
            response = self.llm.invoke(
                prompt.format_messages(
                    current_message=current_message,
                    context=context_text
                )
            )
            
            return {"messages": [response]}
        
        # Add node and compile
        workflow.add_node("translate", translate_query)
        workflow.add_edge(START, "translate")
        
        self.query_translator = workflow.compile(checkpointer=self.memory)
    
    def _translate_query_with_context(self, user_message: str, search_type: str, thread_id: str = "default") -> tuple[str, str]:
        """
        Translate user message using LangGraph memory for context awareness
        
        Args:
            user_message: Raw user input
            search_type: Intended search type
            thread_id: Thread ID for memory context
            
        Returns:
            Tuple of (translated_query, corrected_search_type)
        """
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # Use the query translator workflow
            response = self.query_translator.invoke(
                {"messages": [HumanMessage(content=user_message)]},
                config=config
            )
            
            # Parse the response
            translation_text = response["messages"][-1].content
            
            # Extract query, type, and reasoning
            lines = translation_text.strip().split('\n')
            translated_query = user_message  # fallback
            corrected_type = search_type  # fallback
            
            for line in lines:
                if line.startswith("Query:"):
                    translated_query = line.replace("Query:", "").strip()
                elif line.startswith("Type:"):
                    corrected_type = line.replace("Type:", "").strip()
            
            logger.info(f"🔄 Query translation: '{user_message}' → '{translated_query}' (type: {corrected_type})")
            
            return translated_query, corrected_type
            
        except Exception as e:
            logger.error(f"Error in query translation: {e}")
            # Fallback to simple translation
            return self._simple_query_translation(user_message, search_type)
    
    def _simple_query_translation(self, user_message: str, search_type: str) -> tuple[str, str]:
        """Fallback simple query translation without context"""
        user_lower = user_message.lower()
        
        # Problem detection
        if any(word in user_lower for word in ["chalena", "not working", "problem", "issue", "error"]):
            if "ambition guru" in user_lower or "ambition" in user_lower:
                return f"Ambition Guru app troubleshooting problems not working", "information"
            else:
                return f"troubleshooting problems not working {user_message}", "information"
        
        # Course queries
        if search_type == "products" and any(word in user_lower for word in ["course", "kors", "kun kun"]):
            return f"available courses programs {user_message}", "products"
        
        # Default
        return f"{search_type} {user_message}", search_type

    def _search_with_retriever(self, search_query: str, search_type: str) -> str:
        """
        Perform the actual search using the appropriate retriever

        Args:
            search_query: The processed search query
            search_type: Either "information" or "products"

        Returns:
            Formatted search results
        """
        try:
            if search_type == "products":
                retriever = self.vector_manager.get_product_retriever()
                if retriever is None:
                    return "Sorry, I'm having trouble accessing the course catalog right now. Please try again later."

                docs = retriever.invoke(search_query)
                if not docs:
                    return "I couldn't find any courses matching your query. Could you please be more specific?"

                # Format product results
                results = []
                for doc in docs:
                    content = doc.page_content
                    results.append(f"📚 {content}")

                return "\n\n".join(results)

            else:  # information search
                retriever = self.vector_manager.get_info_retriever()
                if retriever is None:
                    return "Sorry, I'm having trouble accessing the information database right now. Please try again later."

                docs = retriever.invoke(search_query)
                if not docs:
                    return "I couldn't find information about that topic. Could you please rephrase your question?"

                # Format information results
                results = []
                for doc in docs:
                    content = doc.page_content
                    results.append(f"ℹ️ {content}")

                return "\n\n".join(results)

        except Exception as e:
            logger.error(f"Error in search: {str(e)}")
            return f"Sorry, I encountered an error while searching: {str(e)}"

    def search_information(self, user_message: str, thread_id: str = "default") -> str:
        """
        Search for general information using context-aware query translation
        
        Args:
            user_message: The user's original message
            thread_id: Thread ID for memory context
            
        Returns:
            Formatted search results
        """
        # Translate query with conversation context
        search_query, corrected_type = self._translate_query_with_context(
            user_message, "information", thread_id
        )
        
        # Use the corrected search type
        if corrected_type == "products":
            logger.info(f"📋→🎓 Redirecting to products search: '{search_query}'")
            return self._search_with_retriever(search_query, "products")
        else:
            logger.info(f"📋 Information search: '{search_query}'")
            return self._search_with_retriever(search_query, "information")
    
    def search_products(self, user_message: str, thread_id: str = "default") -> str:
        """
        Search for products using context-aware query translation
        
        Args:
            user_message: The user's original message
            thread_id: Thread ID for memory context
            
        Returns:
            Formatted search results
        """
        # Translate query with conversation context
        search_query, corrected_type = self._translate_query_with_context(
            user_message, "products", thread_id
        )
        
        # Use the corrected search type
        if corrected_type == "information":
            logger.info(f"🎓→📋 Redirecting to information search: '{search_query}'")
            return self._search_with_retriever(search_query, "information")
        else:
            logger.info(f"🎓 Products search: '{search_query}'")
            return self._search_with_retriever(search_query, "products")
    
    def get_memory_stats(self, thread_id: str = "default") -> Dict[str, Any]:
        """Get memory statistics for debugging"""
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            state = self.query_translator.get_state(config)
            messages = state.values.get("messages", [])
            
            return {
                "thread_id": thread_id,
                "message_count": len(messages),
                "last_message": messages[-1].content if messages else None,
                "memory_type": "LangGraph MemorySaver"
            }
        except Exception as e:
            return {
                "thread_id": thread_id,
                "error": str(e)
            }
