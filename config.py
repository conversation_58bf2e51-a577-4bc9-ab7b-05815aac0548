"""
Configuration module for vector stores and other shared resources
"""

import os
from typing import Optional
from dotenv import load_dotenv
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient

load_dotenv()


class VectorStoreManager:
    """
    Manages vector stores and embeddings for the application
    Provides global access to avoid repeated initialization
    """
    
    def __init__(self):
        self._embeddings: Optional[OpenAIEmbeddings] = None
        self._info_retriever = None
        self._product_retriever = None
        self._qdrant_client: Optional[QdrantClient] = None
        
    @property
    def embeddings(self) -> OpenAIEmbeddings:
        """Get or create embeddings instance"""
        if self._embeddings is None:
            self._embeddings = OpenAIEmbeddings(
                model="text-embedding-3-large",
                dimensions=1536,
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )
        return self._embeddings
    
    @property
    def qdrant_client(self) -> QdrantClient:
        """Get or create Qdrant client"""
        if self._qdrant_client is None:
            qdrant_url = os.getenv("QDRANT_URL", "http://localhost:6333")
            qdrant_api_key = os.getenv("QDRANT_API_KEY")
            
            if qdrant_api_key:
                self._qdrant_client = QdrantClient(
                    url=qdrant_url,
                    api_key=qdrant_api_key
                )
            else:
                self._qdrant_client = QdrantClient(url=qdrant_url)
        return self._qdrant_client
    
    def get_info_retriever(self):
        """Get information retriever"""
        if self._info_retriever is None:
            try:
                info_collection = os.getenv("QDRANT_INFO_COLLECTION", "information")
                vector_store = QdrantVectorStore(
                    client=self.qdrant_client,
                    collection_name=info_collection,
                    embedding=self.embeddings
                )
                self._info_retriever = vector_store.as_retriever(
                    search_type="similarity",
                    search_kwargs={"k": 5}
                )
            except Exception as e:
                print(f"Warning: Could not initialize info retriever: {e}")
                self._info_retriever = None
        return self._info_retriever
    
    def get_product_retriever(self):
        """Get product retriever"""
        if self._product_retriever is None:
            try:
                product_collection = os.getenv("QDRANT_PRODUCT_COLLECTION", "products")
                vector_store = QdrantVectorStore(
                    client=self.qdrant_client,
                    collection_name=product_collection,
                    embedding=self.embeddings
                )
                self._product_retriever = vector_store.as_retriever(
                    search_type="similarity",
                    search_kwargs={"k": 5}
                )
            except Exception as e:
                print(f"Warning: Could not initialize product retriever: {e}")
                self._product_retriever = None
        return self._product_retriever


# Global instance
_vector_store_manager: Optional[VectorStoreManager] = None


def get_vector_store_manager() -> VectorStoreManager:
    """
    Get the global vector store manager instance
    Creates one if it doesn't exist
    """
    global _vector_store_manager
    if _vector_store_manager is None:
        _vector_store_manager = VectorStoreManager()
    return _vector_store_manager


# Convenience functions for backward compatibility
def get_info_retriever():
    """Get information retriever"""
    return get_vector_store_manager().get_info_retriever()


def get_product_retriever():
    """Get product retriever"""
    return get_vector_store_manager().get_product_retriever()


def get_embeddings():
    """Get embeddings instance"""
    return get_vector_store_manager().embeddings
