"""
FastAPI application for multi-agent customer service system
Provides login and chat endpoints with <PERSON>W<PERSON> authentication
"""

import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any
import uuid

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
from jose import JWTError, jwt
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError
from motor.motor_asyncio import AsyncIOMotorClient
import uvicorn

from agent_v2.main_agent import MainAgent
from utils import setup_colored_logging, log_info, log_error, log_success

# Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
MONGODB_URL = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
DATABASE_NAME = os.getenv("DATABASE_NAME", "customer_service")

# Initialize FastAPI app
app = FastAPI(
    title="Multi-Agent Customer Service API",
    description="API for customer service with multi-agent system",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()
ph = PasswordHasher()

# Database
client: Optional[AsyncIOMotorClient] = None
db = None

# Agent
main_agent: Optional[MainAgent] = None


# Pydantic models
class LoginRequest(BaseModel):
    email: EmailStr
    password: str


class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    user_id: str
    email: str


class ChatRequest(BaseModel):
    message: str


class ChatResponse(BaseModel):
    response: str
    thread_id: str


class UserInfo(BaseModel):
    user_id: str
    email: str
    name: Optional[str] = None
    tenant_id: Optional[str] = None


# Database functions
async def get_database():
    """Get database connection"""
    global client, db
    if client is None:
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client[DATABASE_NAME]
    return db


async def get_user_by_email(email: str) -> Optional[Dict[str, Any]]:
    """Get user by email from database"""
    database = await get_database()
    user = await database.users.find_one({"email": email})
    return user


async def get_user_by_id(user_id: str) -> Optional[Dict[str, Any]]:
    """Get user by ID from database"""
    database = await get_database()
    user = await database.users.find_one({"_id": user_id})
    return user


# Authentication functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password using Argon2"""
    try:
        ph.verify(hashed_password, plain_password)
        return True
    except VerifyMismatchError:
        return False


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserInfo:
    """Get current user from JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = await get_user_by_id(user_id)
    if user is None:
        raise credentials_exception
    
    return UserInfo(
        user_id=user["_id"],
        email=user["email"],
        name=user.get("name"),
        tenant_id=user.get("tenant_id")
    )


# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    global main_agent
    
    # Setup logging
    setup_colored_logging()
    log_info("Starting Multi-Agent Customer Service API")
    
    # Initialize database
    await get_database()
    log_success("Database connection established")
    
    # Initialize main agent
    try:
        main_agent = MainAgent()
        log_success("Main agent initialized")
    except Exception as e:
        log_error("Failed to initialize main agent", e)
        raise


# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global client
    if client:
        client.close()
        log_info("Database connection closed")


# API Routes
@app.post("/login", response_model=LoginResponse)
async def login(login_request: LoginRequest):
    """
    Login endpoint that provides JWT token
    """
    try:
        # Get user from database
        user = await get_user_by_email(login_request.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password"
            )
        
        # Verify password
        if not verify_password(login_request.password, user["password"]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password"
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user["_id"]}, expires_delta=access_token_expires
        )
        
        log_success(f"User {login_request.email} logged in successfully")
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            user_id=user["_id"],
            email=user["email"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        log_error("Login failed", e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@app.post("/chat", response_model=ChatResponse)
async def chat(
    chat_request: ChatRequest,
    current_user: UserInfo = Depends(get_current_user)
):
    """
    Chat endpoint that uses current agent with user's thread ID
    """
    try:
        if not main_agent:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Agent not initialized"
            )
        
        # Use user ID as thread ID for conversation continuity
        thread_id = current_user.user_id
        
        log_info(f"Chat request from user {current_user.email}: {chat_request.message[:50]}...")
        
        # Get response from main agent
        response = main_agent.chat(chat_request.message, thread_id)
        
        log_success(f"Chat response generated for user {current_user.email}")
        
        return ChatResponse(
            response=response,
            thread_id=thread_id
        )
        
    except Exception as e:
        log_error("Chat failed", e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat request"
        )


@app.get("/me", response_model=UserInfo)
async def get_me(current_user: UserInfo = Depends(get_current_user)):
    """
    Get current user information
    """
    return current_user


@app.get("/health")
async def health_check():
    """
    Health check endpoint
    """
    return {"status": "healthy", "timestamp": datetime.utcnow()}


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
